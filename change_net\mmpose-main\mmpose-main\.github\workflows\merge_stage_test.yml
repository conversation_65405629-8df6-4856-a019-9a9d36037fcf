name: merge_stage_test

on:
  push:
    paths-ignore:
      - 'README.md'
      - 'README_zh-CN.md'
      - 'docs/**'
      - 'demo/**'
      - '.dev_scripts/**'
      - '.circleci/**'
    branches:
      - dev-1.x

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_cpu_py:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: [3.8, 3.9]
        torch: [1.8.1]
        include:
          - torch: 1.8.1
            torchvision: 0.9.1
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: pip install pip --upgrade
      - name: Install Numpy
        run: pip install -U numpy
      - name: Install PyTorch
        run: pip install torch==${{matrix.torch}}+cpu torchvision==${{matrix.torchvision}}+cpu -f https://download.pytorch.org/whl/torch_stable.html
      - name: Install MMEngine
        run: pip install git+https://github.com/open-mmlab/mmengine.git@main
      - name: Install MMCV
        run: |
          pip install -U openmim
          mim install 'mmcv >= 2.0.0'
      - name: Install MMDet
        run: |
          python -m pip install --upgrade pip setuptools wheel
          pip install git+https://github.com/open-mmlab/mmdetection.git@dev-3.x
      - name: Install other dependencies
        run: |
          pip install -r requirements/tests.txt
          pip install -r requirements/runtime.txt
          pip install -r requirements/albu.txt
          pip install -r requirements/poseval.txt
      - name: Build and install
        run: rm -rf .eggs && pip install -e .
      - name: Run unittests and generate coverage report
        run: |
          coverage run --branch --source mmpose -m pytest tests/
          coverage xml
          coverage report -m

  build_cpu_pt:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: [3.7]
        torch: [1.8.0, 1.8.1, 1.9.1, 1.10.1, 1.11.0, 1.12.1, 1.13.0]
        include:
          - torch: 1.8.0
            torchvision: 0.9.0
          - torch: 1.8.1
            torchvision: 0.9.1
          - torch: 1.9.1
            torchvision: 0.10.1
          - torch: 1.10.1
            torchvision: 0.11.2
          - torch: 1.11.0
            torchvision: 0.12.0
          - torch: 1.12.1
            torchvision: 0.13.1
          - torch: 1.13.0
            torchvision: 0.14.0
          - torch: 2.0.0
            torchvision: 0.15.1
            python-version: 3.8
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: pip install pip --upgrade
      - name: Install Numpy
        run: pip install -U numpy
      - name: Install PyTorch
        run: pip install torch==${{matrix.torch}}+cpu torchvision==${{matrix.torchvision}}+cpu -f https://download.pytorch.org/whl/torch_stable.html
      - name: Install MMEngine
        run: pip install git+https://github.com/open-mmlab/mmengine.git@main
      - name: Install MMCV
        run: |
          pip install -U openmim
          mim install 'mmcv >= 2.0.0'
      - name: Install MMDet
        run: |
          python -m pip install --upgrade pip setuptools wheel
          pip install git+https://github.com/open-mmlab/mmdetection.git@dev-3.x
      - name: Install other dependencies
        run: |
          pip install -r requirements/tests.txt
          pip install -r requirements/runtime.txt
          pip install -r requirements/albu.txt
          pip install -r requirements/poseval.txt
      - name: Build and install
        run: rm -rf .eggs && pip install -e .
      - name: Run unittests and generate coverage report
        run: |
          coverage run --branch --source mmpose -m pytest tests/
          coverage xml
          coverage report -m
      # Only upload coverage report for python3.7 && pytorch1.8.1 cpu
      - name: Upload coverage to Codecov
        if: ${{matrix.torch == '1.8.1' && matrix.python-version == '3.7'}}
        uses: codecov/codecov-action@v1.0.14
        with:
          file: ./coverage.xml
          flags: unittests
          env_vars: OS,PYTHON
          name: codecov-umbrella
          fail_ci_if_error: false

  build_cu102:
    runs-on: ubuntu-22.04
    container:
      image: pytorch/pytorch:1.8.1-cuda10.2-cudnn7-devel
    strategy:
      matrix:
        python-version: [3.7]
        include:
          - torch: 1.8.1
            cuda: 10.2
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: pip install pip --upgrade
      - name: Fetch GPG keys
        run: |
          apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/cuda/repos/ubuntu1804/x86_64/3bf863cc.pub
          apt-key adv --fetch-keys https://developer.download.nvidia.com/compute/machine-learning/repos/ubuntu1804/x86_64/7fa2af80.pub
      - name: Install Python-dev
        run: apt-get update && apt-get install -y python${{matrix.python-version}}-dev
        if: ${{matrix.python-version != 3.9}}
      - name: Install system dependencies
        run: |
          apt-get update && apt-get install -y ffmpeg libsm6 libxext6 git ninja-build libglib2.0-0 libsm6 libxrender-dev libxext6
      - name: Install mmpose dependencies
        run: |
          pip install -U numpy
          pip install git+https://github.com/open-mmlab/mmengine.git@main
          pip install -U openmim
          mim install 'mmcv >= 2.0.0'
          pip install git+https://github.com/open-mmlab/mmdetection.git@dev-3.x
          pip install -r requirements/tests.txt
          pip install -r requirements/runtime.txt
          pip install -r requirements/albu.txt
          pip install -r requirements/poseval.txt
      - name: Build and install
        run: rm -rf .eggs && pip install -e .
      - name: Run unittests and generate coverage report
        run: |
          coverage run --branch --source mmpose -m pytest tests/
          coverage xml
          coverage report -m

  build_windows:
    runs-on: windows-2022
    strategy:
      matrix:
        os: [windows-2022]
        python: [3.7]
        platform: [cpu, cu111]
        torch: [1.8.1]
        torchvision: [0.9.1]
        include:
          - python-version: 3.8
            platform: cu117
            torch: 2.0.0
            torchvision: 0.15.1
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: python -m pip install pip --upgrade
      - name: Install lmdb
        run: python -m pip install lmdb
      - name: Install PyTorch
        run: python -m pip install torch==${{matrix.torch}}+${{matrix.platform}} torchvision==${{matrix.torchvision}}+${{matrix.platform}} -f https://download.pytorch.org/whl/${{matrix.platform}}/torch_stable.html
      - name: Install mmpose dependencies
        run: |
          python -m pip install -U numpy
          python -m pip install --upgrade pip setuptools wheel
          python -m pip install git+https://github.com/open-mmlab/mmengine.git@main
          python -m pip install -U openmim
          mim install 'mmcv >= 2.0.0'
          mim install git+https://github.com/open-mmlab/mmdetection.git@dev-3.x
          python -m pip install -r requirements/tests.txt
          python -m pip install -r requirements/runtime.txt
          python -m pip install -r requirements/albu.txt
          python -m pip install -r requirements/poseval.txt
      - name: Build and install
        run: |
          python -m pip install -e . -v
      - name: Run unittests and generate coverage report
        run: |
          pytest tests/
