Collections:
- Name: RTMPose
  Paper:
    Title: "RTMPose: Real-Time Multi-Person Pose Estimation based on MMPose"
    URL: https://arxiv.org/abs/2303.07399
  README: https://github.com/open-mmlab/mmpose/blob/main/projects/rtmpose/README.md
Models:
- Config: configs/body_2d_keypoint/rtmpose/body8/rtmpose-t_8xb1024-700e_body8-halpe26-256x192.py
  In Collection: RTMPose
  Metadata:
    Architecture: &id001
    - RTMPose
    Training Data: &id002
    - AI Challenger
    - COCO
    - CrowdPose
    - MPII
    - sub-JHMDB
    - Halpe
    - PoseTrack18
  Name: rtmpose-t_8xb1024-700e_body8-halpe26-256x192
  Results:
  - Dataset: Body8
    Metrics:
      Mean@0.1: 0.919
      AUC: 0.664
    Task: Body 2D Keypoint
  Weights: https://download.openmmlab.com/mmpose/v1/projects/rtmposev1/rtmpose-t_simcc-body7_pt-body7-halpe26_700e-256x192-6020f8a6_20230605.pth
- Config: configs/body_2d_keypoint/rtmpose/body8/rtmpose-s_8xb1024-700e_body8-halpe26-256x192.py
  In Collection: RTMPose
  Metadata:
    Architecture: *id001
    Training Data: *id002
  Name: rtmpose-s_8xb1024-700e_body8-halpe26-256x192
  Results:
  - Dataset: Body8
    Metrics:
      Mean@0.1: 0.930
      AUC: 0.682
    Task: Body 2D Keypoint
  Weights: https://download.openmmlab.com/mmpose/v1/projects/rtmposev1/rtmpose-s_simcc-body7_pt-body7-halpe26_700e-256x192-7f134165_20230605.pth
- Config: configs/body_2d_keypoint/rtmpose/body8/rtmpose-m_8xb512-700e_body8-halpe26-256x192.py
  In Collection: RTMPose
  Alias:  body26
  Metadata:
    Architecture: *id001
    Training Data: *id002
  Name: rtmpose-m_8xb512-700e_body8-halpe26-256x192
  Results:
  - Dataset: Body8
    Metrics:
      Mean@0.1: 0.947
      AUC: 0.719
    Task: Body 2D Keypoint
  Weights: https://download.openmmlab.com/mmpose/v1/projects/rtmposev1/rtmpose-m_simcc-body7_pt-body7-halpe26_700e-256x192-4d3e73dd_20230605.pth
- Config: configs/body_2d_keypoint/rtmpose/body8/rtmpose-l_8xb512-700e_body8-halpe26-256x192.py
  In Collection: RTMPose
  Metadata:
    Architecture: *id001
    Training Data: *id002
  Name: rtmpose-l_8xb512-700e_body8-halpe26-256x192
  Results:
  - Dataset: Body8
    Metrics:
      Mean@0.1: 0.954
      AUC: 0.732
    Task: Body 2D Keypoint
  Weights: https://download.openmmlab.com/mmpose/v1/projects/rtmposev1/rtmpose-l_simcc-body7_pt-body7-halpe26_700e-256x192-2abb7558_20230605.pth
- Config: configs/body_2d_keypoint/rtmpose/body8/rtmpose-m_8xb512-700e_body8-halpe26-384x288.py
  In Collection: RTMPose
  Metadata:
    Architecture: *id001
    Training Data: *id002
  Name: rtmpose-m_8xb512-700e_body8-halpe26-384x288
  Results:
  - Dataset: Body8
    Metrics:
      Mean@0.1: 0.952
      AUC: 0.736
    Task: Body 2D Keypoint
  Weights: https://download.openmmlab.com/mmpose/v1/projects/rtmposev1/rtmpose-m_simcc-body7_pt-body7-halpe26_700e-384x288-89e6428b_20230605.pth
- Config: configs/body_2d_keypoint/rtmpose/body8/rtmpose-l_8xb512-700e_body8-halpe26-384x288.py
  In Collection: RTMPose
  Metadata:
    Architecture: *id001
    Training Data: *id002
  Name: rtmpose-l_8xb512-700e_body8-halpe26-384x288
  Results:
  - Dataset: Body8
    Metrics:
      Mean@0.1: 0.956
      AUC: 0.744
    Task: Body 2D Keypoint
  Weights: https://download.openmmlab.com/mmpose/v1/projects/rtmposev1/rtmpose-l_simcc-body7_pt-body7-halpe26_700e-384x288-734182ce_20230605.pth
- Config: configs/body_2d_keypoint/rtmpose/body8/rtmpose-x_8xb256-700e_body8-halpe26-384x288.py
  In Collection: RTMPose
  Metadata:
    Architecture: *id001
    Training Data: *id002
  Name: rtmpose-x_8xb256-700e_body8-halpe26-384x288
  Results:
  - Dataset: Body8
    Metrics:
      Mean@0.1: 0.957
      AUC: 0.748
    Task: Body 2D Keypoint
  Weights: https://download.openmmlab.com/mmpose/v1/projects/rtmposev1/rtmpose-x_simcc-body7_pt-body7-halpe26_700e-384x288-7fb6e239_20230606.pth
