# 2D Face Landmark Detection

2D face landmark detection (also referred to as face alignment) is defined as the task of detecting the face keypoints from an input image.

Normally, the input images are cropped face images, where the face locates at the center;
or the rough location (or the bounding box) of the hand is provided.

## Data preparation

Please follow [DATA Preparation](/docs/en/dataset_zoo/2d_face_keypoint.md) to prepare data.

## Demo

Please follow [Demo](/demo/docs/en/2d_face_demo.md) to run demos.

<img src="https://user-images.githubusercontent.com/11788150/109144943-ccd44900-779c-11eb-9e9d-8682e7629654.gif" width="600px" alt><br>
